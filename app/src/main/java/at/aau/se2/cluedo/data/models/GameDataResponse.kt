package at.aau.se2.cluedo.data.models

import com.google.gson.annotations.SerializedName

/**
 * Response DTO containing game data and player information
 */
data class GameDataResponse(
    @SerializedName("lobbyId") 
    val lobbyId: String = "",
    
    @SerializedName("players") 
    val players: List<Player> = emptyList(),
    
    @SerializedName("gameBoard") 
    val gameBoard: GameBoard? = null,
    
    @SerializedName("currentTurnState") 
    val currentTurnState: TurnStateResponse? = null,
    
    @SerializedName("success") 
    val success: Boolean = true,
    
    @SerializedName("message") 
    val message: String = ""
)
